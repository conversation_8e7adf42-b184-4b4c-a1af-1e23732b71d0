import { useState, useEffect, useRef } from 'react';
import { saveLead } from './lib/supabaseClient';
import ChatHeader from './components/ChatHeader';
import MessageList from './components/MessageList';
import LeadForm from './components/LeadForm';
import ChatInput from './components/ChatInput';
import MinimizedButton from './components/MinimizedButton';

export default function ChatWidget() {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([
    { text: "Hi there! 👋 I'm the UpZera assistant. Ask me anything about our services or company!", isUser: false }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [companyKnowledge, setCompanyKnowledge] = useState({});
  const [isBotTyping, setIsBotTyping] = useState(false);
  const [leadForm, setLeadForm] = useState({
    name: '',
    email: '',
    isSubmitting: false,
    error: null
  });
  const [showLeadForm, setShowLeadForm] = useState(false);
  const [showCalendly, setShowCalendly] = useState(false);
  const [isConversationEnded, setIsConversationEnded] = useState(false);
  const [screenSize, setScreenSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800,
    isSmall: false,
  });
  const chatContainerRef = useRef(null);

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      setTimeout(() => {
        chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
      }, 100);
    }
  };

  // Set up CSS variables based on screen size
  useEffect(() => {
    const root = document.documentElement;
    if (screenSize.isSmall) {
      // Mobile-friendly sizes - different variations based on actual width
      if (screenSize.width <= 320) { // iPhone SE, smaller devices
        root.style.setProperty('--chat-font-size', '13px');
        root.style.setProperty('--chat-padding', '6px');
      } else if (screenSize.width <= 375) { // iPhone X/11/12 mini
        root.style.setProperty('--chat-font-size', '14px');
        root.style.setProperty('--chat-padding', '8px');
      } else { // Other mobile devices
        root.style.setProperty('--chat-font-size', '15px');
        root.style.setProperty('--chat-padding', '10px');
      }
    } else {
      // Reset to default sizes
      root.style.setProperty('--chat-font-size', '16px');
      root.style.setProperty('--chat-padding', '16px');
    }
  }, [screenSize]);

  // Listen for screen size changes internally too
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      const isSmall = width <= 768; // Consider tablets and phones as "small"
      
      setScreenSize({ width, height, isSmall });
    };

    // Set initial size
    handleResize();
    
    // Add resize listener
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    const allowedOrigins = ['https://upzera-web.netlify.app', 'http://localhost:3000', 'https://chatbot-test123.netlify.app'];
    const handleMessage = (event) => {
      if (!allowedOrigins.includes(event.origin)) return;
      
      if (event.data.type === 'toggleChat') {
        setIsOpen(event.data.payload.isOpen);
      }
      else if (event.data.type === 'closeChatbot') {
        setIsOpen(false);
      }
      else if (event.data.type === 'responsiveView') {
        // Handle responsive view message from parent
        const { isSmallScreen, width, height } = event.data.payload;
        
        // Update our screen size state based on parent information
        setScreenSize({
          width: width || window.innerWidth,
          height: height || window.innerHeight,
          isSmall: isSmallScreen
        });
        
        console.log(`Received screen info from parent: ${width}x${height}, isSmall: ${isSmallScreen}`);
      }
    };
    
    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, showLeadForm]);

  useEffect(() => {
    fetch('/company_knowledge.json')
      .then(res => {
        if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);
        return res.json();
      })
      .then(data => setCompanyKnowledge(data))
      .catch(err => {
        console.error('Error loading knowledge base:', err);
        alert('Failed to load company knowledge. Please check console for details.');
      });
  }, []);

  const findKnowledgeMatch = (question) => {
    const lower = question.toLowerCase();
    if (/speak|talk|connect|chat|contact|representative|person|human|agent|real/i.test(lower)) return null;
    for (const faq of companyKnowledge.faqs || []) {
      if (faq.keywords.some(k => lower.includes(k))) return faq.answer;
    }
    for (const service of companyKnowledge.services || []) {
      if (service.keywords.some(k => lower.includes(k))) return `${service.name}: ${service.description}`;
    }
    if (/when was (upzera|company) (founded|established|created|started)/i.test(lower)) {
      return `UpZera was founded in ${companyKnowledge.company_info?.founding_year || 2020}`;
    }
    if (/hello|hi|hey/.test(lower)) {
      return companyKnowledge.conversation_flows?.greetings[
        Math.floor(Math.random() * companyKnowledge.conversation_flows.greetings.length)
      ];
    }
    return null;
  };

  const getAIResponse = async (userInput) => {
    try {
      const prompt = `
        You are a friendly and helpful assistant for UpZera, a tech company based in Eindhoven. Your goal is to assist users in a conversational and approachable manner.
        Company services:
        ${JSON.stringify(companyKnowledge.services?.map(s => `${s.name}: ${s.description}`) || [])}
        Common questions:
        ${JSON.stringify(companyKnowledge.faqs?.map(f => f.question) || [])}
        Current conversation context:
        ${messages.slice(-3).map(m => m.isUser ? `User: ${m.text}` : `${m.text}`).join('\n')}
        User asked: "${userInput}"
        Please provide a friendly, helpful, and conversational response in 1-2 sentences. Avoid overly technical jargon unless necessary. Be encouraging and positive.`;

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_OPENAI_API_KEY}`
        },
        body: JSON.stringify({
          model: 'gpt-4.1-nano',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.7
        })
      });
      const data = await response.json();
      return data.choices[0]?.message?.content?.trim() || "Hmm, I couldn't quite generate a response for that. Could you try asking in a different way?";
    } catch (error) {
      console.error('AI Error:', error);
      return "Oops! It seems I'm having a little trouble connecting right now. Please give it another try in a moment.";
    }
  };

  const validateEmail = (email) => /^[^\s@]+@[^\s@]+$/.test(email);

  const handleLeadSubmit = async (e) => {
    e.preventDefault();
    if (!validateEmail(leadForm.email)) {
      setLeadForm(prev => ({ ...prev, error: 'Please enter a valid email' }));
      return;
    }
    if (!leadForm.name.trim()) {
      setLeadForm(prev => ({ ...prev, error: 'Please enter your name' }));
      return;
    }
    setLeadForm(prev => ({ ...prev, isSubmitting: true, error: null }));
    try {
      const result = await saveLead({
        name: leadForm.name,
        email: leadForm.email,
        created_at: new Date().toISOString()
      });
      if (result.error) throw new Error(result.error.message || 'Failed to save your information');
      setMessages(prev => [...prev, { text: `Thank you ${leadForm.name}! Our team will contact you shortly.`, isUser: false }]);
      setShowLeadForm(false);
      setLeadForm({ name: '', email: '', isSubmitting: false, error: null });
    } catch (error) {
      setLeadForm(prev => ({ ...prev, isSubmitting: false, error: error.message || 'Failed to submit. Please try again later.' }));
    }
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;
    setMessages(prev => [...prev, { text: inputValue, isUser: true }]);
    setInputValue('');
    setIsBotTyping(true);

    // Check for farewell intent
    if (/bye|goodbye|adios|see you|cya|farewell/i.test(inputValue.toLowerCase())) {
      setMessages(prev => [
        ...prev,
        { text: "Goodbye! Have a great day! 👋", isUser: false }
      ]);
      setIsConversationEnded(true);
      setIsBotTyping(false);
      return;
    }

    // Check for scheduling or booking intent
    if (/schedule|book|reserve|appointment|meeting/i.test(inputValue.toLowerCase())) {
      setMessages(prev => [
        ...prev,
        { text: "Sure! You can easily book a meeting with us below:", isUser: false },
        { type: 'calendly', isUser: false },
        { text: "Great! Just let me know your preferred date and time, and I’ll help you set it up. We're excited to connect with you! 😊", isUser: false }
      ]);
      setIsBotTyping(false);
      return;
    }

    setTimeout(async () => {
      const localAnswer = findKnowledgeMatch(inputValue);
      if (localAnswer) {
        setMessages(prev => [...prev, { text: localAnswer, isUser: false }]);
        setIsBotTyping(false);
      } else {
        const aiResponse = await getAIResponse(inputValue);
        setMessages(prev => [...prev, { text: aiResponse, isUser: false }]);
        setIsBotTyping(false);
      }
    }, 1500);
  };

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {isOpen ? (
        <div className={`bg-white rounded-2xl shadow-xl flex flex-col animate-fade-in border-0 ${
          screenSize.isSmall 
            ? screenSize.width <= 360 
              ? 'w-[280px] h-[480px]' // Extra small devices
              : 'w-[320px] h-[520px]' // Medium to small devices
            : 'w-[350px] h-[550px]'   // Regular devices
        }`}>
          <ChatHeader 
            onClose={() => {
              setIsOpen(false);
              window.parent.postMessage({ type: 'closeChatbot' }, '*');
            }} 
            screenSize={screenSize}
          />

          <MessageList 
            messages={messages} 
            isBotTyping={isBotTyping} 
            ref={chatContainerRef}
            screenSize={screenSize}
          />

          {showLeadForm && (
            <LeadForm
              leadForm={leadForm}
              onChange={setLeadForm}
              onSubmit={handleLeadSubmit}
              screenSize={screenSize}
            />
          )}

          <ChatInput
            inputValue={inputValue}
            onChange={setInputValue}
            onSend={handleSendMessage}
            disabled={isConversationEnded}
            screenSize={screenSize}
          />
        </div>
      ) : (
        <MinimizedButton 
          onClick={() => setIsOpen(true)} 
          screenSize={screenSize}
        />
      )}
    </div>
  );
}
